<?xml version="1.0" encoding="UTF-8"?>
<document type="freeswitch/xml">
    <section name="dialplan">
        <context name="default">
            <extension name="reroute" continue="true">
                <condition>
                    <action application="answer"/>

                    <!-- This DialPlan was first created based on the Jira request TPS-1285 -->

                    <!-- CREDENTIALS -->
                    <!--Set JWT for SMS API-->
                    <action application="set"
                            data="erp_auth_header='Authorization: Bearer HIDDEN'"
                            inline="true"/>

                    <!-- FETCHING INFO FROM ERP -->
                    <action application="curl"
                            data="https://erpbackendpro.maids.cc/sales/client/getnumberinfoforziwo?mobileNumber=${caller_id_number}"
                            inline="true"/>

                    <!-- Extract caller's name -->
                    <!--<action application="set"-->
                    <!--data='name=${regex(${curl_response_data}|"name"\s*:\s*"([^"]+)"|%1|b)}'-->
                    <!--inline="true"/>-->

                    <!-- Extract caller's type (client/maid) -->
                    <!--<action application="set"-->
                    <!--data='type=${regex(${curl_response_data}|"type"\s*:\s*"([^"]+)"|%1|b)}'-->
                    <!--inline="true"/>-->

                    <!-- Extract caller's status whether it's a client or a maid -->
                    <action application="set"
                            data='status=${regex(${curl_response_data}|"status"\s*:\s*"([^"]+)"|%1|b)}'
                            inline="true"/>

                    <!-- Extract whether the called client's maid is live-in or live-out (in case it was a client) -->
                    <!--<action application="set"-->
                    <!--data='live_status=${regex(${curl_response_data}|"live_status"\s*:\s*"([^"]+)"|%1|b)}'-->
                    <!--inline="true"/>-->

                    <!-- Extract whether the called client has recently switched to MV contract or not (in case it was a client) -->
                    <!--<action application="set"-->
                    <!--data='switched_recently_to_mv=${regex(${curl_response_data}|"switched_recently"\s*:\s*"([^"]+)"|%1|b)}'-->
                    <!--inline="true"/>-->
                    
                    <!-- Call ERP recruitment endpoint to check if the caller is a maid applicant -->
                    <action application="curl"
                            data="https://erpbackendpro.maids.cc/recruitment/chat-gpt/get-maids-at-candidate-wa/?mobileNumber=${caller_id_number}"
                            inline="true"/>

                    <!-- Extract the application status to determine if the caller is a maid applicant -->
                    <action application="set"
                            data='is_maid_applicant=${regex(${curl_response_data}|"applicationStatus"\s*:\s*"([^"]+)"|%1|b)}'
                            inline="true"/>
                            
                    
                    <!-- Check whether the caller is maid and liveout -->
                    <action application="curl"
                            data="https://erpbackendpro.maids.cc/staffmgmt/housemaid/search-housemaid-or-maid-at-by-number?phone=${caller_id_number}"
                            inline="true"/>

                    <action application="set"
                            data='is_housemaid_liveout=${regex(${curl_response_data}|"housemaid_liveout"\s*:\s*(true)|%1|b)}'
                            inline="true"/>

                </condition>


                <!-- LOGIC -->
                <!--Active CC Client-->
                <condition field="${status}" expression="active_cc" break="on-true">
                    <action application="log" data="Transfer CC Client to RBs Queue" inline="true"/>
                    <action application="transfer" data="000098"/>
                </condition>

                <!--Active MV Client-->
                <condition field="${status}" expression="active_mv" break="on-true">
                    <!-- Transfer to MV Clients DialPlan -->
                    <action application="transfer" data="1010000"/>
                </condition>

                <!--Active CC and MV-->
                <condition field="${status}" expression="active_cc_and_mv" break="on-true">
                    <action application="log" data="Transfer CC/MV Client to RBs Queue" inline="true"/>
                    <action application="transfer" data="000098"/>
                </condition>

                <!-- Condition for Maid Applicant -->
                <condition field="${is_maid_applicant}" expression="ACTIVE" break="on-true">
                    <!-- Immediately transfer maid applicants to their designated DialPlan -->
                    <action application="transfer" data="5555556"/>
                </condition>

                <!-- Transfer to MV Clients DialPlan whenever it's a live-out maid -->
                <condition field="${is_housemaid_liveout}" expression="^(true)$" break="on-true">
                    <action application="transfer" data="7020000"/>
                </condition>

                <condition field="${status}" expression="^(not_found|false|no_active)$" break="on-true">
                    <!-- Caller is a prospect -->
                    <action application="log" data="Call will go to Enchanter_Line Queue" inline="true"/>
                    <action application="transfer" data="000002"/>


                    <!-- Otherwise, it's an active Maid -->
                    <!-- Play her an audio -->
                    <anti-action application="playback"
                                 data="$${CALLCENTER_STORAGE_URL}/e4e0e6452e192b8673d1711359144756"/>
                    <!--Send her a WA message through API-->
                    <anti-action application="set" data='post={}' inline="true"/>
                    <anti-action application="curl"
                                 data="'https://erpbackendpro.maids.cc/admin/send-whatsapp/send-template?phoneNumber=${caller_id_number}&skill=DELIGHTERS&livepersonTemplate=HXe70ec39f41a53bd4eff3fd1e21746cc3&outboundNumber=971505741759' headers content-type application/json append_headers ${erp_auth_header} append_headers 'pagecode: AdminSendWhatsappMessageFromZiwo' post ${post}"
                                 inline="true"/>
                    <!--<anti-action application="hangup"/>-->
                </condition>

            </extension>
        </context>
    </section>
</document>