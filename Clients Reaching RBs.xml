<?xml version="1.0" encoding="UTF-8"?>
<document type="freeswitch/xml">
    <section name="dialplan">
        <context name="default">
            <extension name="reroute" continue="true">
                <condition>
                    <action application="answer"/>

                    <!-- First created based on <PERSON>ra request TPS-1129 -->

                    <!-- CREDENTIALS -->
                    <!--Set ERP auth header-->
                    <action application="set"
                            data="erp_auth_header='Authorization: Bearer HIDDEN'"
                            inline="true"/>


                    <!-- FETCHING INFO FROM ERP -->
                    <action application="curl"
                            data="https://erpbackendpro.maids.cc/sales/client/getnumberinfoforziwo?mobileNumber=${caller_id_number}"
                            inline="true"/>

                    <!-- Extract caller's name -->
                    <action application="set"
                            data='name=${regex(${curl_response_data}|"name"\s*:\s*"([^"]+)"|%1|b)}'
                            inline="true"/>

                    <!-- Extract caller's type (client/maid) -->
                    <!--<action application="set"-->
                    <!--data='type=${regex(${curl_response_data}|"type"\s*:\s*"([^"]+)"|%1|b)}'-->
                    <!--inline="true"/>-->

                    <!-- Extract caller's status whether it's a client or a maid -->
                    <action application="set"
                            data='status=${regex(${curl_response_data}|"status"\s*:\s*"([^"]+)"|%1|b)}'
                            inline="true"/>

                    <!-- Extract whether the called client's maid is live-in or live-out (in case it was a client) -->
                    <action application="set"
                            data='live_status=${regex(${curl_response_data}|"live_status"\s*:\s*"([^"]+)"|%1|b)}'
                            inline="true"/>

                    <!-- Extract whether the called client has recently switched to MV contract or not (in case it was a client) -->
                    <action application="set"
                            data='switched_recently_to_mv=${regex(${curl_response_data}|"switched_recently"\s*:\s*"([^"]+)"|%1|b)}'
                            inline="true"/>

                    <!-- Check whether the caller is maid and liveout -->
                    <action application="curl"
                            data="https://erpbackendpro.maids.cc/staffmgmt/housemaid/search-housemaid-or-maid-at-by-number?phone=${caller_id_number}"
                            inline="true"/>

                    <action application="set"
                            data='is_housemaid_liveout=${regex(${curl_response_data}|"housemaid_liveout"\s*:\s*(true)|%1|b)}'
                            inline="true"/>

                    <!-- Call ERP recruitment endpoint to check if the caller is a maid applicant -->
                    <action application="curl"
                            data="https://erpbackendpro.maids.cc/recruitment/chat-gpt/get-maids-at-candidate-wa/?mobileNumber=${caller_id_number}"
                            inline="true"/>

                    <!-- Extract the application status to determine if the caller is a maid applicant -->
                    <action application="set"
                            data='is_maid_applicant=${regex(${curl_response_data}|"applicationStatus"\s*:\s*"([^"]+)"|%1|b)}'
                            inline="true"/>

                    <!-- Log to G chat if needed -->
                    <!-- <action application="set" data='post={"text":"${live_status}"}' inline="true"/>-->
                    <!-- <action application="curl"-->
                    <!-- data="'https://chat.googleapis.com/v1/spaces/AAAAXCJBgrw/messages?key=AIzaSyDdI0hCZtE6vySjMm-WEfRq3CPzqKqqsHI&token=1rA5on99Jn3vDrYlHewAUI9FT0WW8gaZ1r5zW1Uq5Bo' headers content-type application/json post ${post}"-->
                    <!-- inline="true"/>-->

                </condition>


                <!-- LOGIC -->
                <!--Active CC client-->
                <condition field="${status}" expression="active_cc" break="on-true">
                    <!--Transfer to SR RB Queue.xml DialPlan-->
                    <action application="log" data="Transfer CC Client to RBs Queue" inline="true"/>
                    <action application="transfer" data="000098"/>
                </condition>

                <!--Active CC and MV-->
                <condition field="${status}" expression="active_cc_and_mv" break="on-true">
                    <!--Transfer to SR RB Queue.xml DialPlan-->
                    <action application="log" data="Transfer CC/MV Client to SR RB Queue.xml" inline="true"/>
                    <action application="transfer" data="000098"/>
                </condition>

                <!--Active MV client-->
                <condition field="${status}" expression="active_mv" break="never">
                    <!--Switched recently to MV-->
                    <condition field="${switched_recently_to_mv}" expression="true" break="never">
                        <!--Transfer to SR RB Queue.xml DialPlan-->
                        <action application="transfer" data="000098"/>

                        <!-- Otherwise -->
                        <!-- Transfer to MV Clients DialPlan -->
                        <anti-action application="transfer" data="1010000"/>
                    </condition>
                </condition>

                <!-- Transfer to MV Clients DialPlan whenever it's a live-out maid -->
                <condition field="${is_housemaid_liveout}" expression="^(true)$" break="on-true">
                    <action application="transfer" data="7020000"/>
                </condition>

                <!-- Condition for Live-Out clients -->
                <condition field="${live_status}" expression="Live-Out" break="on-true">
                    <!-- Transfer to liveout queue -->
                    <action application="transfer" data="7020000"/>
                </condition>

                <!-- Condition for Maid Applicant -->
                <condition field="${is_maid_applicant}" expression="ACTIVE" break="on-true">
                    <!-- Immediately transfer maid applicants to their designated DialPlan -->
                    <action application="transfer" data="5555556"/>
                </condition>

                <!--Not Active-->
                <!-- Check if the caller is not a maid-->
                <condition field="${status}" expression="^(not_found|false|no_active)$" break="on-true">
                    <!-- Transfer to the Not Active Client DialPlan-->
                    <action application="log" data="Call will go to 'Not Active Client Reaching RBs DialPlan"
                            inline="true"/>
                    <action application="transfer" data="000095"/>

                    <!-- otherwise if it was a maid, transfer her to SR RB Queue.xml DialPlan-->
                    <anti-action application="log" data="The caller is a maid, transfer to RBs"/>
                    <anti-action application="transfer" data="000098"/>
                </condition>
            </extension>
        </context>
    </section>
</document>