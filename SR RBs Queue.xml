<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<document type="freeswitch/xml">
    <section name="dialplan">
        <context name="default">
            <extension name="smart_routing" continue="true">
                <condition>
                    <action application="answer"/>

                    <!-- This is where we route clients to agents -->

                    <!-- CREDENTIALS -->
                    <!-- Set Ziwo auth header for API Authentication -->
                    <action application="set"
                            data="ziwo_auth_header='access_token: HIDDEN'"
                            inline="true"/>

                    <!-- Set ERP auth header for API Authentication -->
                    <action application="set"
                            data="erp_auth_header='Authorization: Bearer HIDDEN'"
                            inline="true"/>


                    <!-- SMART ROUTING INFORMATION -->
                    <!-- Set the routing list mapping agent IDs to their corresponding extension numbers -->
                    <action application="set"
                            data='routing_list={"_1_A01":"4010101","_1_A02":"4010102","_1_A03":"4010103","_1_A04":"4010104","_1_A05":"4010105","_1_A06":"4010106","_1_A07":"4010107","_1_A08":"4010108","_1_A09":"4010109","_1_A10":"4010110","_1_A11":"4010111","_1_A12":"4010112","_1_A13":"4010113","_1_A14":"4010114","_1_A15":"4010115","_2_A01":"4010201","_2_A02":"4010202","_2_A03":"4010203","_2_A04":"4010204","_2_A05":"4010205","_2_A06":"4010206","_2_A07":"4010207","_2_A08":"4010208","_2_A09":"4010209","_2_A10":"4010210","_2_A11":"4010211","_2_A12":"4010212","_2_A13":"4010213","_2_A14":"4010214","_2_A15":"4010215","_3_A01":"4010301","_3_A02":"4010302","_3_A03":"4010303","_3_A04":"4010304","_3_A05":"4010305","_3_A06":"4010306","_3_A07":"4010307","_3_A08":"4010308","_3_A09":"4010309","_3_A10":"4010310","_3_A11":"4010311","_3_A12":"4010312","_3_A13":"4010313","_3_A14":"4010314","_3_A15":"4010315","_4_A01":"4010401","_4_A02":"4010402","_4_A03":"4010403","_4_A04":"4010404","_4_A05":"4010405","_4_A06":"4010406","_4_A07":"4010407","_4_A08":"4010408","_4_A09":"4010409","_4_A10":"4010410","_4_A11":"4010411","_4_A12":"4010412","_4_A13":"4010413","_4_A14":"4010414","_4_A15":"4010415","_5_A01":"4010501","_5_A02":"4010502","_5_A03":"4010503","_5_A04":"4010504","_5_A05":"4010505","_5_A06":"4010506","_5_A07":"4010507","_5_A08":"4010508","_5_A09":"4010509","_5_A10":"4010510","_5_A11":"4010511","_5_A12":"4010512","_5_A13":"4010513","_5_A14":"4010514","_5_A15":"4010515","_6_A01":"4010601","_6_A02":"4010602","_6_A03":"4010603","_6_A04":"4010604","_6_A05":"4010605","_6_A06":"4010606","_6_A07":"4010607","_6_A08":"4010608","_6_A09":"4010609","_6_A10":"4010610","_6_A11":"4010611","_6_A12":"4010612","_6_A13":"4010613","_6_A14":"4010614","_6_A15":"4010615","_7_A01":"4010701","_7_A02":"4010702","_7_A03":"4010703","_7_A04":"4010704","_7_A05":"4010705","_7_A06":"4010706","_7_A07":"4010707","_7_A08":"4010708","_7_A09":"4010709","_7_A10":"4010710","_7_A11":"4010711","_7_A12":"4010712","_7_A13":"4010713","_7_A14":"4010714","_7_A15":"4010715"}'
                            inline="true"/>

                    <!-- Retrieve smart routing assignment information for the caller's number (whether he/she's previously linked to an agent or not) -->
                    <action inline="true" application="curl"
                            data="'https://maidscc-api.aswat.co/admin/blacklists?search=${regex(${caller_id_number}|^\+([0-9]+)$|%1)}' append_headers ${ziwo_auth_header}"/>

                    <!-- Retrieve whether the client is assigned to an agent or not -->
                    <!-- Note: the b parameter in the regex() returns 'false' when no matches were found -->
                    <action application="set"
                            data='saved_previous_agent=${regex(${curl_response_data}|"originalName":"SR_RB(_[0-9]+_A[0-9]{2})\.xml"|%1|b)}'
                            inline="true"/>

                    <!-- Retrieve whether the client is found in the current routing list or not -->
                    <action application="set"
                            data='previous_agent=${regex(${routing_list}|(${saved_previous_agent})|%1|b)}'
                            inline="true"/>

                    <!-- If assigned, retrieve the agent-based queue number corresponding to the SR assignment from the routing list -->
                    <action application="set"
                            data='previous_agent_queue_number=${regex(${routing_list}|"${previous_agent}":"([0-9]+)"|%1)}'
                            inline="true"/>

                    <!-- FETCHING INFO FROM ERP -->
                    <action application="curl"
                            data="https://erpbackendpro.maids.cc/sales/client/getnumberinfoforziwo?mobileNumber=${caller_id_number}"
                            inline="true"/>

                    <!-- Extract caller's name -->
                    <!--<action application="set"-->
                    <!--data='name=${regex(${curl_response_data}|"name"\s*:\s*"([^"]+)"|%1|b)}'-->
                    <!--inline="true"/>-->

                    <!-- Extract caller's type (client/maid) -->
                    <!--<action application="set"-->
                    <!--data='type=${regex(${curl_response_data}|"type"\s*:\s*"([^"]+)"|%1|b)}'-->
                    <!--inline="true"/>-->

                    <!-- Extract caller's status whether it's a client or a maid -->
                    <action application="set"
                            data='status=${regex(${curl_response_data}|"status"\s*:\s*"([^"]+)"|%1|b)}'
                            inline="true"/>

                    <!-- Extract whether the called client's maid is live-in or live-out (in case it was a client) -->
                    <action application="set"
                            data='live_status=${regex(${curl_response_data}|"live_status"\s*:\s*"([^"]+)"|%1|b)}'
                            inline="true"/>

                    <!-- Extract whether the called client has recently switched to MV contract or not (in case it was a client) -->
                    <action application="set"
                            data='switched_recently_to_mv=${regex(${curl_response_data}|"switched_recently"\s*:\s*"([^"]+)"|%1|b)}'
                            inline="true"/>

                    <!--Log caller's info to the MS Teams channel-->
                    <!--<action application="set"-->
                    <!--data="msg_to_log='-->
                    <!--Name: ${name}\n-->
                    <!--Type: ${type}\n-->
                    <!--Status: ${status}\n-->
                    <!--Live-in/Live-out Status: ${live_status}\n-->
                    <!--Recently Switched to MV: ${switched_recently_to_mv}\n'"-->
                    <!--inline="true"/>-->
                    <!--<action application="set" data='post={"text":"${msg_to_log}"}' inline="true"/>-->
                    <!--<action application="curl"-->
                    <!--data="'https://maidcc.webhook.office.com/webhookb2/80f644d8-b585-45ec-8946-fd5a4df487e1@c1f6807c-76ad-4bbf-ad83-4c922934dfdc/IncomingWebhook/9cce84add3194077a77f154ef088879a/d31881d4-9ea8-439b-82c1-7d67932fc896' headers content-type application/json post ${post}"-->
                    <!--inline="true"/>-->
                </condition>


                <!-- LOGIC -->
                <!-- If client status is 'active_mv' -->
                <condition field="${status}" expression="active_mv" break="never">
                    <condition field="${switched_recently_to_mv}" expression="true" break="never">
                        <!-- If he/she has switched recently to MV-->
                        <!-- Do nothing, just let the flow continues -->


                        <!-- Otherwise, transfer to MV Clients DialPlan -->
                        <anti-action application="transfer" data="1010000"/>

                    </condition>
                </condition>

                <!-- If the maid is Live-Out, transfer her to RB_Liveout queue -->
                <condition field="${live_status}" expression="Live-Out" break="on-true">
                    <action application="transfer" data="7020000"/>
                </condition>

                <!-- If the caller is not assigned to an agent through smart routing, transfer to RBs queue-->
                <condition field="${previous_agent}" expression="^false$" break="on-true">
                    <!-- Play audio to inform them that the agent they were previously talking to is unavailable -->
                    <action application="set"
                            data="cc_moh_override=$${CALLCENTER_STORAGE_URL}/73bd5afa43d401665731692011054054"/>

                    <!-- Transfer to RBs queue while updating smart routing -->
                    <action application="transfer" data="6010000"/>


                    <!-- otherwise (caller is already assigned to an agent), transfer to the agent's queue -->
                    <anti-action application="transfer" data="${previous_agent_queue_number}"/>
                </condition>
            </extension>
        </context>
    </section>
</document>