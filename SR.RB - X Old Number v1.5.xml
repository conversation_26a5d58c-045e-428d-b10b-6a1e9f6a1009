<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<document type="freeswitch/xml">
    <section name="dialplan">
        <context name="default">
            <extension name="date_difference_calculator" continue="true">
                <condition>
                    <action application="answer"/>


                    <!-- Set number -->
                    <!-- Number ID 214: 97145810608 -->
                    <action application="set" data="number_id=214" inline="true"/>


                    <!-- Credentials -->
                    <action application="set"
                            data="ziwo_auth_header='access_token: HIDDEN'"
                            inline="true"/>

                    <action application="set"
                            data="erp_auth_header='Authorization: Bearer HIDDEN'"
                            inline="true"/>

                    <!-- Fetch the client's most recent call -->
                    <action application="curl"
                            data="https://maidscc-api.aswat.co/callHistory/?contactNumber=${regex(${caller_id_number}|^\+?([0-9]+)$|%1)}&limit=1&numbers[]=${number_id} headers content-type application/json append_headers ${ziwo_auth_header}"
                            inline="true"/>
                    <action application="log" data="${curl_response_data}" inline="true"/>

                    <!-- Decompose its date -->
                    <action application="set"
                            data='most_recent_call=${regex(${curl_response_data}|"startedAt":"([0-9]{4}-[0-9]{2}-[0-9]{2})T[0-9:.Z]+".*?"result":"answered"|$1|b)}'
                            inline="true"/>
                    <action application="set"
                            data='most_recent_call_year=${regex(${most_recent_call}|^([0-9]{4})|%1|b)}'
                            inline="true"/>
                    <action application="set"
                            data='most_recent_call_month=${regex(${most_recent_call}|-([0-9]{2})-|%1|b)}'
                            inline="true"/>
                    <action application="set"
                            data='most_recent_call_day=${regex(${most_recent_call}|-([0-9]{2})$|%1|b)}'
                            inline="true"/>

                    <!-- Extract today's date -->
                    <action application="set" data="today_date=${strftime(%Y-%m-%d)}" inline="true"/>
                    <action application="set" data='today_year=${regex(${today_date}|^([0-9]{4})|%1|b)}'
                            inline="true"/>
                    <action application="set" data='today_month=${regex(${today_date}|-([0-9]{2})-|%1|b)}'
                            inline="true"/>
                    <action application="set" data='today_day=${regex(${today_date}|-([0-9]{2})$|%1|b)}'
                            inline="true"/>

                    <!-- Calculate the difference in days -->
                    <action application="set"
                            data='date_difference=${expr(((${today_year}-${most_recent_call_year})*365)+((${today_month}-${most_recent_call_month})*30)+(${today_day}-${most_recent_call_day}))}'
                            inline="true"/>

                </condition>

                <!-- Condition for Maid Applicant -->
                <condition field="${is_maid_applicant}" expression="ACTIVE" break="on-true">
                    <!-- Immediately transfer maid applicants to their designated DialPlan -->
                    <action application="transfer" data="5555556"/>
                </condition>

                <!-- If client hasn't reached us before -->
                <condition field="${most_recent_call}" expression="^false$" break="on-true">
                    <!-- Follow the logic used for MV clients -->
                    <action application="transfer" data="1010000"/>
                </condition>

                <!-- If client has reached us within the last 10 days -->
                <condition field="${date_difference}" expression="^(0|1|2|3|4|5|6|7|8|9|10)$" continue="on-false">

                    <!-- PlEASE NOTE: Here, the client will be redirected to the last tribe talked to, not the last agent -->

                    <!-- The routing list only for tribes -->
                    <action application="set"
                            data='routing_list={"_1":"4010100","_2":"4010200","_3":"4010300","_4":"4010400","_5":"4010500","_6":"4010600","_7":"4010700","_8":"4010800"}'
                            inline="true"/>

                    <!-- Fetch Ziwo Smart Routing list that remembers the bindings between clients numbers and agents -->
                    <action inline="true" application="curl"
                            data="https://maidscc-api.aswat.co/admin/blacklists?search=${regex(${caller_id_number}|^\+?([0-9]+)$|%1)} append_headers ${ziwo_auth_header}"/>
                    <action application="log" data="${curl_response_data}" inline="true"/>

                    <!-- In the regex, we filtered only the tribe's number -->
                    <action application="set"
                            data='sr_tribe=${regex(${curl_response_data}|"extension":".SR_RB(_[0-9]+)"|%1|b)}'
                            inline="true"/>

                    <!-- Get tribe's extension to be transferred to based on routing list -->
                    <action application="set"
                            data='tribe_extension=${regex(${routing_list}|"${sr_tribe}":"([0-9]+)"|%1)}'
                            inline="true"/>

                    <!-- If reached within more than 10 days, direct to MV clients logic -->
                    <anti-action application="transfer" data="1010000"/>
                </condition>

                <!-- If no tribe is linked to this client, take them to the RBs default logic -->
                <condition field="${sr_tribe}" expression="^false$" break="on-true">
                    <action application="transfer" data="6010000"/>

                    <!-- Otherwise, take them to their preferred tribe -->
                    <anti-action application="transfer" data="${tribe_extension}"/>
                </condition>

            </extension>
        </context>
    </section>
</document>