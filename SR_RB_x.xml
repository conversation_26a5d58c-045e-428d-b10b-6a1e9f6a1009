<?xml version="1.0" encoding="UTF-8" standalone="no"?>

<document type="freeswitch/xml">
    <section name="dialplan">
        <context name="default">
            <extension name="smart_routing" continue="true">
                <condition>
                    <action application="answer" />
                    <!-- CREDENTIALS -->

                    <!--Set
                    ERP auth header-->

                    <action application="set" data="erp_auth_header='Authorization: Bearer HIDDEN'"
                        inline="true" />

                    <!-- FETCHING INFO FROM ERP (NOT USED) -->

                    <!--<action
                    application="curl"-->

                    <!--data="https://erpbackendpro.maids.cc/sales/client/getnumberinfoforziwo?mobileNumber=${caller_id_number}"-->
                    <!--inline="true"/>-->
                    <!-- Extract caller's name -->

                    <!--<action
                    application="set"-->

                    <!--data='name=${regex(${curl_response_data}|"name"\s*:\s*"([^"]+)"|%1|b)}'-->
                    <!--inline="true"/>-->
                    <!-- Extract caller's type (client/maid) -->

                    <!--<action
                    application="set"-->

                    <!--data='type=${regex(${curl_response_data}|"type"\s*:\s*"([^"]+)"|%1|b)}'-->
                    <!--inline="true"/>-->
                    <!-- Extract caller's status whether it's a client or a maid -->

                    <!--<action
                    application="set"-->

                    <!--data='status=${regex(${curl_response_data}|"status"\s*:\s*"([^"]+)"|%1|b)}'-->
                    <!--inline="true"/>-->
                    <!-- Extract whether the called client's maid is live-in or live-out (in case it
                    was a client) -->

                    <!--<action
                    application="set"-->

                    <!--data='live_status=${regex(${curl_response_data}|"live_status"\s*:\s*"([^"]+)"|%1|b)}'-->
                    <!--inline="true"/>-->
                    <!-- Extract whether the called client has recently switched to MV contract or
                    not (in case it was a client) -->

                    <!--<action
                    application="set"-->

                    <!--data='switched_recently_to_mv=${regex(${curl_response_data}|"switched_recently"\s*:\s*"([^"]+)"|%1|b)}'-->
                    <!--inline="true"/>-->
                    <!--Log
                    caller's info to the MS Teams channel-->

                    <!--<action
                    application="set"-->

                    <!--data="msg_to_log='-->
                    <!--Name:
                    ${name}\n-->

                    <!--Type:
                    ${type}\n-->

                    <!--Status:
                    ${status}\n-->

                    <!--Live-in/Live-out
                    Status: ${live_status}\n-->

                    <!--Recently
                    Switched to MV: ${switched_recently_to_mv}\n'"-->

                    <!--inline="true"/>-->
                    <!-- QUEUE INFO -->

                    <!--Set
                    queue name-->

                    <!--//TODO:
                    UPDATE TO CORRECT QUEUE-->

                    <action application="set" data="queue_name='SR_RB_1@default'" inline="true" />
                    <!--Extract
                    number of agents online in queue-->

                    <action application="set"
                        data="agents_online=${callcenter_config queue count agents ${queue_name}}"
                        inline="true" />

                    <action application="log" data="${agents_online}" inline="true" />
                    <!--Extract
                    number of agents available in queue -->

                    <!-- This update was performed by Qusai Nasr to reflect the number of agents
                    'waiting' to receive a call instead of all available agents -->

                    <action application="curl"
                        data="https://script.google.com/macros/s/AKfycbxVVKjxTMlq1rUdxbLQiybE4kNpTLYyrn4DQCmt-gM5PC2GIJg1pfJb_5xjMbktB-VP/exec?queue=${queue_name}"
                        inline="true" />

                    <action application="set" data="agents_available=${curl_response_data}"
                        inline="true" />

                    <action application="log" data="${agents_available}" inline="true" />
                    <!-- Log the call to ziwo Logging MS teams channel-->

                    <!--<action
                    application="set"-->

                    <!--data="msg_to_log='<b>${caller_id_number}</b>
                    has reached <b>tribe</b> queue <b>${queue_name}</b> with
                    <b>${agents_available}</b> agents available'"-->

                    <!--inline="true"/>-->
                    <!--<action
                    application="set" data='post={"text":"${msg_to_log}"}' inline="true"/>-->

                    <!--<action
                    application="curl"-->

                    <!--data="'https://maidcc.webhook.office.com/webhookb280f644d8-b585-45ec-8946-fd5a4df487e1@c1f6807c-76ad-4bbf-ad83-4c922934dfdc/IncomingWebhook/9cce84add3194077a77f154ef088879a/d31881d4-9ea8-439b-82c1-7d67932fc896'
                    headers content-type application/json post ${post}"-->

                    <!--inline="true"/>-->
                </condition>
                <!-- LOGIC -->

                <!--<condition
                field="${status}" expression="no_active" break="on-true">-->

                <!--<action
                application="log" data="Client Not Active - Call will go to Enchanter_Line Queue"
                inline="true"/>-->

                <!--Transfer
                to Enchanters_Line Queue-->

                <!--<action
                application="transfer" data="000002"/>-->

                <!--</condition>-->
                <!--<condition
                field="${status}" expression="active_mv" break="on-true">-->

                <!--<action
                application="set" data='post={}' inline="true"/>-->

                <!--<action
                application="playback"
                data="$${CALLCENTER_STORAGE_URL}/b611d2d081a872315e01687521782597"/>-->

                <!--Send
                WA using API Request-->

                <!--<action
                application="curl"
                data="'https://erpbackendpro.maids.cc/admin/send-whatsapp/send-template?phoneNumber=${caller_id_number}&skill=CC%20Resolvers&livepersonTemplate=296793689415075&outboundNumber=971505741759'
                headers content-type application/json append_headers ${erp_auth_header}
                append_headers 'pagecode: AdminSendWhatsappMessageFromZiwo' post ${post}"
                inline="true"/>-->

                <!--</condition>-->
                <!--Action
                if no online agents were found in tribe-->

                <condition field="${agents_online}" expression="^0$" break="on-true">
                    <!--Play
                    hold music-->

                    <!--agents_offline_RBs.mp3-->
                    <action application="set"
                        data="cc_moh_override=$${CALLCENTER_STORAGE_URL}/e1981d79f2c559d9b471698059606163" />

                    <!--transfer
                    call to RBs queue with updating the to-be-redirected-to tribe-->

                    <!--//TODO:
                    UPDATE TO CORRECT QUEUE EXTENSION-->

                    <action application="transfer" data="6010000" />
                </condition>
                <!--Action
                if no agent found available in the tribe-->

                <condition field="${agents_available}" expression="^0$">
                    <!--Play
                    hold music-->

                    <!--Retention
                    Client Music on Hold.mp3-->

                    <action application="set"
                        data="cc_moh_override=$${CALLCENTER_STORAGE_URL}/95b64eb0340c331a4a51691839859293" />

                    <!--Transfer
                    call to RBs queue with updating the to-be-redirected-to tribe-->

                    <!--//TODO:
                    UPDATE TO CORRECT QUEUE EXTENSION-->

                    <action application="transfer" data="6010000" />
                    <!--Action
                    if agents online and available were found in the tribe -->

                    <!--Transfer
                    the call to tribe's queue while updating smart routing-->

                    <!--//TODO:
                    UPDATE TO CORRECT QUEUE EXTENSION-->

                    <anti-action application="transfer" data="6010100" />
                </condition>
            </extension>
        </context>
    </section>
</document>