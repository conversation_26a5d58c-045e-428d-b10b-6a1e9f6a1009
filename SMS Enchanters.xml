<?xml version="1.0" encoding="UTF-8"?>
<document type="freeswitch/xml">
    <section name="dialplan">
        <context name="default">
            <extension name="send_sms" continue="true">
                <condition>
                    <action application="answer"/>
                    <!-- CREDENTIALS -->
                    <!--Set ERP auth header-->
                    <action application="set"
                            data="erp_auth_header='Authorization: Bearer HIDDEN'"
                            inline="true"/>


                    <!-- FETCHING INFO FROM ERP -->
                    <action application="curl"
                            data="https://erpbackendpro.maids.cc/sales/client/getnumberinfoforziwo?mobileNumber=${caller_id_number}"
                            inline="true"/>

                    <!-- Extract caller's name -->
                    <action application="set"
                            data='name=${regex(${curl_response_data}|"name"\s*:\s*"([^"]+)"|%1|b)}'
                            inline="true"/>

                    <!-- Extract caller's first name -->
                    <action application="set" data='first_name=${regex(${name}|(\w+)|$1)}' inline="true"/>

                    <!-- Extract caller's type (client/maid) -->
                    <!--<action application="set"-->
                    <!--data='type=${regex(${curl_response_data}|"type"\s*:\s*"([^"]+)"|%1|b)}'-->
                    <!--inline="true"/>-->

                    <!-- Extract caller's status whether it's a client or a maid -->
                    <action application="set"
                            data='status=${regex(${curl_response_data}|"status"\s*:\s*"([^"]+)"|%1|b)}'
                            inline="true"/>

                    <!-- Extract whether the called client's maid is live-in or live-out (in case it was a client) -->
                    <!--<action application="set"-->
                    <!--data='live_status=${regex(${curl_response_data}|"live_status"\s*:\s*"([^"]+)"|%1|b)}'-->
                    <!--inline="true"/>-->

                    <!-- Extract whether the called client has recently switched to MV contract or not (in case it was a client) -->
                    <!--<action application="set"-->
                    <!--data='switched_recently_to_mv=${regex(${curl_response_data}|"switched_recently"\s*:\s*"([^"]+)"|%1|b)}'-->
                    <!--inline="true"/>-->

                    <!-- Call ERP recruitment endpoint to check if the caller is a maid applicant -->
                    <action application="curl"
                            data="https://erpbackendpro.maids.cc/recruitment/chat-gpt/get-maids-at-candidate-wa/?mobileNumber=${caller_id_number}"
                            inline="true"/>

                    <!-- Extract the application status to determine if the caller is a maid applicant -->
                    <action application="set"
                            data='is_maid_applicant=${regex(${curl_response_data}|"applicationStatus"\s*:\s*"([^"]+)"|%1|b)}'
                            inline="true"/>
                            
                    <!-- Check whether the caller is maid and liveout -->
                    <action application="curl"
                            data="https://erpbackendpro.maids.cc/staffmgmt/housemaid/search-housemaid-or-maid-at-by-number?phone=${caller_id_number}"
                            inline="true"/>

                    <action application="set"
                            data='is_housemaid_liveout=${regex(${curl_response_data}|"housemaid_liveout"\s*:\s*(true)|%1|b)}'
                            inline="true"/>

                </condition>


                <!-- LOGIC -->
                <!--Active CC client-->
                <condition field="${status}" expression="active_cc" break="on-true">
                    <!--Send them an SMS through API-->
                    <action application="set"
                            data='message="Dear ${first_name},\u000AWe’re happy to have you.\u000AYour Maidscc personalized app is the fastest way to get anything you want. To use it, please click on:\u000Ahttps://ccapp.page.link/portal\u000A\u000AIf you absolutely don’t want to use the app, please click on one of these 2 links:\u000AFor any issues related to your maid, call us: https://www.maids.cc/call_us\u000AFor ALL other issues, WhatsApp us: wa.me/971505741759"'
                            inline="true"/>
                    <action application="set" data='post={"to":"${caller_id_number}","text":${message},"type":41510}'
                            inline="true"/>
                    <action application="curl"
                            data="'https://erpbackendpro.maids.cc/admin/sms/send' headers content-type application/json append_headers ${erp_auth_header} append_headers 'pagecode: admin_testSendingSms' post ${post}"
                            inline="true"/>
                    <action application="log" data="Sent SMS to CC Client" inline="true"/>

                    <!--Play audio-->
                    <!--Audio: visa 2.mp3-->
                    <action application="set"
                            data="cc_moh_override=$${CALLCENTER_STORAGE_URL}/a528bbc74d7b9f9a5f81681888552644"/>

                    <!-- Transfer to SR RB Queue's DialPlan-->
                    <action application="transfer" data="000098"/>
                </condition>

                <!--Active MV client-->
                <condition field="${status}" expression="active_mv" break="on-true">
                    <!-- Transfer to MV Clients DialPlan -->
                    <action application="transfer" data="1010000"/>
                </condition>

                <!--Active CC and MV-->
                <condition field="${status}" expression="active_cc_and_mv" break="on-true">
                    <!--Send them an SMS through API-->
                    <action application="set"
                            data='message="Dear ${first_name},\u000AWe’re happy to have you.\u000AYour Maidscc personalized app is the fastest way to get anything you want. To use it, please click on:\u000Ahttps://ccapp.page.link/portal\u000A\u000AIf you absolutely don’t want to use the app, please click on one of these 2 links:\u000AFor any issues related to your maid, call us: https://www.maids.cc/call_us\u000AFor ALL other issues, WhatsApp us: wa.me/971505741759"'
                            inline="true"/>
                    <action application="set" data='post={"to":"${caller_id_number}","text":${message},"type":41510}'
                            inline="true"/>
                    <action application="curl"
                            data="'https://erpbackendpro.maids.cc/admin/sms/send' headers content-type application/json append_headers ${erp_auth_header} append_headers 'pagecode: admin_testSendingSms' post ${post}"
                            inline="true"/>
                    <action application="log" data="Sent SMS to CC/MV Client" inline="true"/>

                    <!-- Play audio-->
                    <action application="set"
                            data="cc_moh_override=$${CALLCENTER_STORAGE_URL}/a528bbc74d7b9f9a5f81681888552644"/>

                    <!-- Transfer to RBs Queue-->
                    <action application="transfer" data="000098"/>
                </condition>
                
                <!-- Transfer to MV Clients DialPlan whenever it's a live-out maid -->
                <condition field="${is_housemaid_liveout}" expression="^(true)$" break="on-true">
                    <action application="transfer" data="7020000"/>
                </condition>
                
                <!-- Condition for Maid Applicant -->
                <condition field="${is_maid_applicant}" expression="ACTIVE" break="on-true">
                    <!-- Immediately transfer maid applicants to their designated DialPlan -->
                    <action application="transfer" data="5555556"/>
                </condition>

                <!--Not active-->
                <condition field="${status}" expression="no_active" break="on-true">
                    <action application="log" data="Call will go to Enchanter_Line Queue" inline="true"/>

                    <!--SEND WA-->
                    <!--<action application="set" data='post={}' inline="true"/>-->
                    <!--Send WA using API Request-->
                    <!--<action application="curl" data="'https://erpbackendpro.maids.cc/admin/send-whatsapp/send-template?phoneNumber=${caller_id_number}&skill=Enchanters%20For%20Paused%20Inbound&livepersonTemplate=199096402474708&outboundNumber=97142479191' headers content-type application/json append_headers 'Authorization: basic eml3bzpjd3pEVDNtR2tAdVBuYW84ZGk4IQ==' append_headers 'pagecode: AdminSendWhatsappMessageFromZiwo' post ${post}" inline="true"/>-->

                    <!--SEND SMS-->
                    <!--<action application="set" data='message="Thank you for calling Maids.cc. Would you be so kind as to WhatsApp us by clicking on this link: https://maids.page.link/tXr6m2e8buoBcdBT6"' inline="true"/>-->
                    <!--Prepare Body Parameters-->
                    <!--<action application="set" data='post={"to":"${caller_id_number}","text":${message},"type":41510}' inline="true"/>-->
                    <!--Send SMS using API Request-->
                    <!--<action application="curl" data="'https://erpbackendpro.maids.cc/admin/sms/send' headers content-type application/json append_headers 'Authorization: basic TWFyay5hOk1hcmsuYUAyMA==' append_headers 'pagecode: admin_testSendingSms' post ${post}" inline="true"/>-->

                    <!--Transfer to Enchanters-->
                    <action application="transfer" data="000002"/>


                    <!--Action taken otherwise-->
                    <!--Transfer to Enchanters-->
                    <anti-action application="log" data="No match go to Enchanters_Line"/>
                    <anti-action application="transfer" data="000002"/>
                </condition>

            </extension>
        </context>
    </section>
</document>

        <!--IGNORE FOR FUTURE REFERENCE-->
        <!--<action application="set" data="api_result=${get_json_value('${curl_response_data}' data 1 test3)}"/>-->
        <!--<action application='set' data='token=${regex(${response}|"token":"([^""]+)"|$1)}' inline="true"/>-->


        <!--Assigns Values for the API-->
        <!--<action application="set" data='message="Dear ${first_name},\u000AWe’re happy to have you.\u000AYour Maidscc personalized app is the fastest way to get anything you want. To use it, please click on:\u000Ahttps://ccapp.page.link/portal\u000A\u000AIf you absolutely don’t want to use the app, please click on one of these 2 links:\u000AFor any issues related to your maid, call us: 045810693\u000AFor ALL other issues, WhatsApp us: wa.me/971505741759"' inline="true"/>-->
        <!--<action application="set" data='post={"to":"96176629216","text":${message},"type":41510}' inline="true"/>-->


        <!--<action application="set" data='post={"to":"96176629216","text":"Dear ${first_name}\u000AYo","type":41510}' inline="true"/>-->
        <!--<action application="curl" data="'https://erpbackendpro.maids.cc/admin/sms/send' headers content-type application/json append_headers 'Authorization: basic TWFyay5hOk1hcmsuYUAyMA==' append_headers 'pagecode: admin_testSendingSms' post ${post}" inline="true"/>-->

        <!--<action application="curl" data="'https://erpbackendpro.maids.cc/admin/sms/send' headers content-type application/json append_headers 'Authorization: basic TWFyay5hOk1hcmsuYUAyMA==' append_headers 'pagecode: admin_testSendingSms' post ${post}" inline="true"/>-->
        <!--<action application="transfer" data="1233"/>-->